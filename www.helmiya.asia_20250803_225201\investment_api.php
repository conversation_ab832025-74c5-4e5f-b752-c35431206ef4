<?php
// 直接的Investment API实现
header('Content-Type: application/json; charset=utf-8');

// 数据库配置
try {
    $pdo = new PDO("mysql:host=localhost;dbname=helmiya;charset=utf8", 'helmiya', 'helmiya');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (Exception $e) {
    echo json_encode(['code' => 0, 'msg' => '数据库连接失败: ' . $e->getMessage()]);
    exit;
}

$action = $_GET['action'] ?? '';
$user_id = intval($_GET['user_id'] ?? 0);

switch ($action) {
    case 'getInvestmentList':
        if (!$user_id) {
            echo json_encode(['code' => 0, 'msg' => '用户ID不能为空']);
            exit;
        }
        
        try {
            $stmt = $pdo->prepare("SELECT * FROM zm_investment WHERE user_id = ? ORDER BY buy_date DESC");
            $stmt->execute([$user_id]);
            $investments = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'code' => 1,
                'msg' => '获取成功',
                'data' => $investments
            ]);
        } catch (Exception $e) {
            echo json_encode(['code' => 0, 'msg' => '查询失败: ' . $e->getMessage()]);
        }
        break;
        
    case 'addInvestment':
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['code' => 0, 'msg' => '请使用POST方法']);
            exit;
        }
        
        $symbol = $_POST['symbol'] ?? '';
        $name = $_POST['name'] ?? '';
        $buy_price = floatval($_POST['buy_price'] ?? 0);
        $buy_shares = floatval($_POST['buy_shares'] ?? 0);
        $buy_amount = floatval($_POST['buy_amount'] ?? 0);
        $buy_date = $_POST['buy_date'] ?? '';
        $notes = $_POST['notes'] ?? '';
        
        if (!$user_id || !$symbol || !$name || !$buy_price || !$buy_shares || !$buy_date) {
            echo json_encode(['code' => 0, 'msg' => '参数不完整']);
            exit;
        }
        
        try {
            $stmt = $pdo->prepare("INSERT INTO zm_investment (user_id, symbol, name, buy_price, buy_shares, buy_amount, buy_date, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([$user_id, $symbol, $name, $buy_price, $buy_shares, $buy_amount, $buy_date, $notes]);
            
            echo json_encode([
                'code' => 1,
                'msg' => '添加成功',
                'data' => ['id' => $pdo->lastInsertId()]
            ]);
        } catch (Exception $e) {
            echo json_encode(['code' => 0, 'msg' => '添加失败: ' . $e->getMessage()]);
        }
        break;
        
    case 'deleteInvestment':
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['code' => 0, 'msg' => '请使用POST方法']);
            exit;
        }
        
        $id = intval($_POST['id'] ?? 0);
        
        if (!$id || !$user_id) {
            echo json_encode(['code' => 0, 'msg' => '参数不完整']);
            exit;
        }
        
        try {
            $stmt = $pdo->prepare("DELETE FROM zm_investment WHERE id = ? AND user_id = ?");
            $stmt->execute([$id, $user_id]);
            
            if ($stmt->rowCount() > 0) {
                echo json_encode(['code' => 1, 'msg' => '删除成功']);
            } else {
                echo json_encode(['code' => 0, 'msg' => '记录不存在或无权限删除']);
            }
        } catch (Exception $e) {
            echo json_encode(['code' => 0, 'msg' => '删除失败: ' . $e->getMessage()]);
        }
        break;
        
    case 'test':
        echo json_encode([
            'code' => 1,
            'msg' => 'Investment API工作正常',
            'time' => date('Y-m-d H:i:s'),
            'user_id' => $user_id
        ]);
        break;
        
    default:
        echo json_encode(['code' => 0, 'msg' => '未知操作: ' . $action]);
}
?>
