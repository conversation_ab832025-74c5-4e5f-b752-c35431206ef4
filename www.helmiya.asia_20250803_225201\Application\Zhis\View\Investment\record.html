<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>投资记账</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="__PUBLIC__/common/lib/layui/css/layui.css" media="all">
    <style>
        body { margin: 0; padding: 0; background: #f5f5f5; font-family: "Microsoft YaHei", Arial, sans-serif; }
        .container { display: flex; height: 100vh; }
        
        /* 左侧边栏 */
        .sidebar {
            width: 200px;
            background: #393D49;
            color: #fff;
            overflow-y: auto;
        }
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #4C5061;
        }
        .sidebar-header h3 {
            margin: 0;
            font-size: 16px;
            color: #fff;
        }
        .sidebar-menu {
            padding: 0;
            margin: 0;
            list-style: none;
        }
        .sidebar-menu li {
            border-bottom: 1px solid #4C5061;
        }
        .sidebar-menu li a {
            display: block;
            padding: 15px 20px;
            color: #C2C2C2;
            text-decoration: none;
            transition: all 0.3s;
        }
        .sidebar-menu li a:hover,
        .sidebar-menu li.active a {
            background: #009688;
            color: #fff;
        }
        .sidebar-menu li a i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
        }
        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        /* 页面标题 */
        .page-header {
            background: #fff;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .page-title {
            margin: 0;
            font-size: 20px;
            color: #333;
        }
        .page-subtitle {
            margin: 5px 0 0 0;
            color: #666;
            font-size: 14px;
        }
        
        /* 投资概览卡片 */
        .overview-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .overview-card {
            background: #fff;
            padding: 20px;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            text-align: center;
        }
        .card-value {
            font-size: 24px;
            font-weight: bold;
            color: #009688;
            margin-bottom: 5px;
        }
        .card-label {
            color: #666;
            font-size: 14px;
        }
        
        /* 投资记录表格区域 */
        .record-section {
            background: #fff;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .section-header {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        .section-actions {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary { background: #009688; color: #fff; }
        .btn-default { background: #f0f0f0; color: #333; }
        .btn:hover { opacity: 0.8; }
        
        /* 搜索表单 */
        .search-form {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            gap: 15px;
            align-items: center;
        }
        .search-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        /* 投资记录表格 */
        .record-table {
            width: 100%;
            border-collapse: collapse;
        }
        .record-table th,
        .record-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        .record-table th {
            background: #fafafa;
            font-weight: bold;
            color: #333;
        }
        .record-table tbody tr:hover {
            background: #f9f9f9;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-hold { background: #e8f5e8; color: #52c41a; }
        .status-sold { background: #fff2e8; color: #fa8c16; }
        
        /* 图表区域 */
        .chart-section {
            background: #fff;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .chart-container {
            height: 300px;
            background: #f9f9f9;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
        }
        
        /* 操作按钮 */
        .action-btn {
            padding: 4px 8px;
            margin: 0 2px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            text-decoration: none;
        }
        .action-edit { background: #1890ff; color: #fff; }
        .action-delete { background: #ff4d4f; color: #fff; }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h3>财富管理</h3>
            </div>
            <ul class="sidebar-menu">
                <li class="active">
                    <a href="javascript:;">
                        <i class="layui-icon layui-icon-home"></i>
                        投资概览
                    </a>
                </li>
                <li>
                    <a href="javascript:;">
                        <i class="layui-icon layui-icon-edit"></i>
                        记账管理
                    </a>
                </li>
                <li>
                    <a href="javascript:;">
                        <i class="layui-icon layui-icon-chart"></i>
                        投资分析
                    </a>
                </li>
                <li>
                    <a href="javascript:;">
                        <i class="layui-icon layui-icon-set"></i>
                        设置
                    </a>
                </li>
            </ul>
        </div>
        
        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">投资记账</h1>
                <p class="page-subtitle">管理您的投资记录，跟踪投资表现</p>
            </div>
            
            <!-- 投资概览卡片 -->
            <div class="overview-cards">
                <div class="overview-card">
                    <div class="card-value">¥125,680</div>
                    <div class="card-label">总投资金额</div>
                </div>
                <div class="overview-card">
                    <div class="card-value">¥138,750</div>
                    <div class="card-label">当前市值</div>
                </div>
                <div class="overview-card">
                    <div class="card-value">+10.4%</div>
                    <div class="card-label">总收益率</div>
                </div>
                <div class="overview-card">
                    <div class="card-value">8</div>
                    <div class="card-label">持仓数量</div>
                </div>
            </div>
            
            <!-- 投资记录表格 -->
            <div class="record-section">
                <div class="section-header">
                    <div class="section-title">投资记录</div>
                    <div class="section-actions">
                        <a href="javascript:;" class="btn btn-primary">添加记录</a>
                        <a href="javascript:;" class="btn btn-default">导出数据</a>
                    </div>
                </div>
                
                <div class="search-form">
                    <input type="text" class="search-input" placeholder="搜索股票代码或名称">
                    <select class="search-input">
                        <option>全部状态</option>
                        <option>持有中</option>
                        <option>已卖出</option>
                    </select>
                    <button class="btn btn-primary">搜索</button>
                </div>
                
                <table class="record-table">
                    <thead>
                        <tr>
                            <th>股票代码</th>
                            <th>股票名称</th>
                            <th>购买价格</th>
                            <th>购买数量</th>
                            <th>投资金额</th>
                            <th>购买日期</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>000001</td>
                            <td>平安银行</td>
                            <td>¥12.50</td>
                            <td>1000</td>
                            <td>¥12,500</td>
                            <td>2024-01-15</td>
                            <td><span class="status-badge status-hold">持有中</span></td>
                            <td>
                                <a href="javascript:;" class="action-btn action-edit">编辑</a>
                                <a href="javascript:;" class="action-btn action-delete">删除</a>
                            </td>
                        </tr>
                        <tr>
                            <td>000002</td>
                            <td>万科A</td>
                            <td>¥25.80</td>
                            <td>500</td>
                            <td>¥12,900</td>
                            <td>2024-01-10</td>
                            <td><span class="status-badge status-hold">持有中</span></td>
                            <td>
                                <a href="javascript:;" class="action-btn action-edit">编辑</a>
                                <a href="javascript:;" class="action-btn action-delete">删除</a>
                            </td>
                        </tr>
                        <tr>
                            <td>600036</td>
                            <td>招商银行</td>
                            <td>¥35.20</td>
                            <td>300</td>
                            <td>¥10,560</td>
                            <td>2024-01-08</td>
                            <td><span class="status-badge status-sold">已卖出</span></td>
                            <td>
                                <a href="javascript:;" class="action-btn action-edit">编辑</a>
                                <a href="javascript:;" class="action-btn action-delete">删除</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 投资趋势图表 -->
            <div class="chart-section">
                <div class="section-title" style="margin-bottom: 20px;">投资趋势</div>
                <div class="chart-container">
                    <div>📈 投资趋势图表区域<br>（可集成图表库显示投资变化）</div>
                </div>
            </div>
        </div>
    </div>

    <script src="__PUBLIC__/common/lib/layui/layui.js"></script>
    <script>
        // 简单的交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 侧边栏菜单点击
            const menuItems = document.querySelectorAll('.sidebar-menu li');
            menuItems.forEach(item => {
                item.addEventListener('click', function() {
                    menuItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                });
            });
            
            // 删除按钮确认
            const deleteButtons = document.querySelectorAll('.action-delete');
            deleteButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    if(confirm('确定要删除这条投资记录吗？')) {
                        alert('删除功能待实现');
                    }
                });
            });
            
            // 编辑按钮
            const editButtons = document.querySelectorAll('.action-edit');
            editButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    alert('编辑功能待实现');
                });
            });
        });
    </script>
</body>
</html>
