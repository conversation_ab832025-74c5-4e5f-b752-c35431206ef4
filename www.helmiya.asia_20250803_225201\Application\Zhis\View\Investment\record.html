<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>投资记账</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: #f0f2f5;
            color: #333;
        }
        .container {
            display: flex;
            height: 100vh;
        }

        /* 左侧边栏 */
        .sidebar {
            width: 80px;
            background: #fff;
            border-right: 1px solid #e8e8e8;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px 0;
        }
        .sidebar-item {
            width: 60px;
            height: 60px;
            margin-bottom: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s;
        }
        .sidebar-item:hover {
            background: #f0f2f5;
        }
        .sidebar-item.active {
            background: #e6f7ff;
            color: #1890ff;
        }
        .sidebar-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        .sidebar-text {
            font-size: 12px;
            text-align: center;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        /* 顶部标题栏 */
        .header-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .header-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .header-actions {
            display: flex;
            gap: 10px;
        }
        .header-btn {
            padding: 6px 12px;
            background: #f0f2f5;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }

        /* 投资详情卡片 */
        .investment-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .investment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .investment-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        .investment-code {
            color: #666;
            font-size: 14px;
        }
        .investment-details {
            margin-bottom: 20px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        .detail-label {
            color: #666;
        }
        .detail-value {
            color: #333;
            font-weight: 500;
        }

        /* 图表区域 */
        .chart-section {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .chart-header {
            margin-bottom: 20px;
        }
        .chart-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        .chart-container {
            height: 200px;
            position: relative;
            background: #fafafa;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .chart-canvas {
            width: 100%;
            height: 100%;
        }

        /* 操作按钮区域 */
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        .action-btn {
            padding: 8px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            background: #1890ff;
            color: #fff;
            transition: all 0.3s;
        }
        .action-btn:hover {
            background: #40a9ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-item active">
                <div class="sidebar-icon">🏠</div>
                <div class="sidebar-text">首页</div>
            </div>
            <div class="sidebar-item">
                <div class="sidebar-icon">📊</div>
                <div class="sidebar-text">投资概览</div>
            </div>
            <div class="sidebar-item">
                <div class="sidebar-icon">📝</div>
                <div class="sidebar-text">记账管理</div>
            </div>
            <div class="sidebar-item">
                <div class="sidebar-icon">📈</div>
                <div class="sidebar-text">投资分析</div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 顶部标题栏 -->
            <div class="header-bar">
                <div class="header-title">投资记账</div>
                <div class="header-actions">
                    <button class="header-btn">导出</button>
                    <button class="header-btn">设置</button>
                </div>
            </div>

            <!-- 投资详情卡片 -->
            <div class="investment-card">
                <div class="investment-header">
                    <div>
                        <div class="investment-title">投资记录详情</div>
                        <div class="investment-code">代码: 000001</div>
                    </div>
                </div>

                <div class="investment-details">
                    <div class="detail-row">
                        <span class="detail-label">投资金额:</span>
                        <span class="detail-value">¥12,500.00</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">当前价值:</span>
                        <span class="detail-value">¥13,200.00</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">收益率:</span>
                        <span class="detail-value" style="color: #52c41a;">+5.6%</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">持有天数:</span>
                        <span class="detail-value">45天</span>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="chart-header">
                    <div class="chart-title">价格走势</div>
                </div>
                <div class="chart-container">
                    <canvas id="priceChart" class="chart-canvas"></canvas>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="action-btn">买入记录</button>
                    <button class="action-btn">卖出记录</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 绘制简单的折线图
        document.addEventListener('DOMContentLoaded', function() {
            const canvas = document.getElementById('priceChart');
            const ctx = canvas.getContext('2d');

            // 设置canvas尺寸
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            // 模拟数据点
            const dataPoints = [
                {x: 50, y: 120},
                {x: 80, y: 110},
                {x: 120, y: 115},
                {x: 160, y: 105},
                {x: 200, y: 125},
                {x: 240, y: 130},
                {x: 280, y: 120},
                {x: 320, y: 135},
                {x: 360, y: 140},
                {x: 400, y: 145}
            ];

            // 绘制折线图
            ctx.strokeStyle = '#1890ff';
            ctx.lineWidth = 2;
            ctx.beginPath();

            dataPoints.forEach((point, index) => {
                if (index === 0) {
                    ctx.moveTo(point.x, point.y);
                } else {
                    ctx.lineTo(point.x, point.y);
                }
            });

            ctx.stroke();

            // 绘制数据点
            ctx.fillStyle = '#1890ff';
            dataPoints.forEach(point => {
                ctx.beginPath();
                ctx.arc(point.x, point.y, 3, 0, 2 * Math.PI);
                ctx.fill();
            });

            // 侧边栏点击事件
            const sidebarItems = document.querySelectorAll('.sidebar-item');
            sidebarItems.forEach(item => {
                item.addEventListener('click', function() {
                    sidebarItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html>
