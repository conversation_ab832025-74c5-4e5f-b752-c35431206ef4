<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>ETF投资驾驶舱</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="__PUBLIC__/common/lib/layui/css/layui.css" media="all">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        .page-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        /* 导航栏 */
        .nav-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #fff;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        .nav-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .nav-actions {
            display: flex;
            gap: 15px;
        }
        .nav-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            background: #f8f9fa;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            border: 1px solid #e9ecef;
        }
        .nav-btn:hover {
            background: #e9ecef;
        }
        .nav-btn.active {
            background: #1890ff;
            color: #fff;
            border-color: #1890ff;
        }
        .nav-icon {
            font-size: 16px;
        }
        .nav-text {
            font-size: 14px;
        }

        /* 资产概览卡片 */
        .asset-overview-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            padding: 30px;
            border-radius: 16px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        }
        .total-asset-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .total-label {
            font-size: 16px;
            opacity: 0.9;
        }
        .total-value {
            font-size: 32px;
            font-weight: bold;
        }
        .details-row {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }
        .detail-item {
            text-align: center;
        }
        .detail-label {
            display: block;
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 5px;
        }
        .detail-value {
            display: block;
            font-size: 18px;
            font-weight: bold;
        }
        .positive { color: #52c41a; }
        .negative { color: #ff4d4f; }

        /* 数据看板网格 */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        /* 持仓列表卡片 */
        .holdings-card {
            background: #fff;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
        }
        .card-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .card-action {
            color: #1890ff;
            cursor: pointer;
            font-size: 14px;
        }

        /* 持仓项目 */
        .holding-item {
            padding: 15px 0;
            border-bottom: 1px solid #f8f9fa;
            transition: all 0.3s;
        }
        .holding-item:last-child {
            border-bottom: none;
        }
        .holding-item:hover {
            background: #fafbfc;
            margin: 0 -20px;
            padding: 15px 20px;
            border-radius: 8px;
        }
        .holding-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        .holding-name {
            font-size: 16px;
            font-weight: 500;
            color: #333;
        }
        .holding-code {
            font-size: 12px;
            color: #999;
            background: #f0f0f0;
            padding: 2px 6px;
            border-radius: 4px;
            margin-left: 8px;
        }
        .holding-profit {
            font-size: 16px;
            font-weight: bold;
        }
        .holding-details {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: #666;
        }

        /* 统计卡片 */
        .stats-card {
            background: #fff;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            text-align: center;
        }
        .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        .stat-change {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 12px;
        }
        .stat-change.up {
            background: #f6ffed;
            color: #52c41a;
        }
        .stat-change.down {
            background: #fff2f0;
            color: #ff4d4f;
        }

        /* 趋势图表 */
        .trend-chart {
            background: #fff;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            grid-column: span 2;
        }
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .time-selector {
            display: flex;
            gap: 8px;
        }
        .time-btn {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: #fff;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }
        .time-btn.active {
            background: #1890ff;
            color: #fff;
            border-color: #1890ff;
        }
        .chart-container {
            height: 300px;
            background: #fafafa;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 导航栏 -->
        <div class="nav-bar">
            <div class="nav-title">ETF投资驾驶舱</div>
            <div class="nav-actions">
                <div class="nav-btn" onclick="refreshData()">
                    <span class="nav-icon">🔄</span>
                    <span class="nav-text">刷新</span>
                </div>
                <div class="nav-btn" onclick="goToTrading()">
                    <span class="nav-icon">📊</span>
                    <span class="nav-text">交易</span>
                </div>
                <div class="nav-btn active" onclick="goToTrend()">
                    <span class="nav-icon">📈</span>
                    <span class="nav-text">趋势</span>
                </div>
                <div class="nav-btn" onclick="goToSettings()">
                    <span class="nav-icon">⚙️</span>
                    <span class="nav-text">设置</span>
                </div>
            </div>
        </div>

        <!-- 资产概览卡片 -->
        <div class="asset-overview-card">
            <div class="total-asset-row">
                <span class="total-label">总资产</span>
                <span class="total-value">¥<span id="totalAsset">125,680.00</span></span>
            </div>
            <div class="details-row">
                <div class="detail-item">
                    <span class="detail-label">收益率</span>
                    <span class="detail-value positive" id="profitRate">+8.5%</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">总份额</span>
                    <span class="detail-value" id="totalShares">12,568</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">总成本</span>
                    <span class="detail-value" id="totalCost">¥115,890.00</span>
                </div>
            </div>
        </div>

        <!-- 数据看板 -->
        <div class="dashboard-grid">
            <!-- 持仓列表 -->
            <div class="holdings-card">
                <div class="card-header">
                    <div class="card-title">持仓ETF</div>
                    <div class="card-action" onclick="addHolding()">+ 添加</div>
                </div>
                <div class="holdings-list" id="holdingsList">
                    <!-- 持仓项目将通过JavaScript动态生成 -->
                </div>
            </div>

            <!-- 统计数据 -->
            <div class="stats-card">
                <div class="stat-value" id="holdingCount">8</div>
                <div class="stat-label">持仓数量</div>
                <div class="stat-change up">+2 本月</div>
            </div>

            <div class="stats-card">
                <div class="stat-value positive" id="todayProfit">+¥1,250</div>
                <div class="stat-label">今日收益</div>
                <div class="stat-change up">+1.2%</div>
            </div>

            <!-- 趋势图表 -->
            <div class="trend-chart">
                <div class="card-header">
                    <div class="card-title">资产趋势</div>
                    <div class="time-selector">
                        <div class="time-btn active" data-days="7">7天</div>
                        <div class="time-btn" data-days="30">30天</div>
                        <div class="time-btn" data-days="90">90天</div>
                        <div class="time-btn" data-days="365">1年</div>
                    </div>
                </div>
                <div class="chart-container">
                    <div>📈 资产趋势图表<br>（集成图表库显示资产变化趋势）</div>
                </div>
            </div>
        </div>
    </div>

    <script src="__PUBLIC__/common/lib/layui/layui.js"></script>
    <script>
        // 模拟持仓数据
        const holdingsData = [
            {
                code: '510300',
                name: '沪深300ETF',
                shares: 2000,
                cost: 4.85,
                current: 5.12,
                profit: 5.57,
                amount: 10240
            },
            {
                code: '159919',
                name: '沪深300ETF',
                shares: 1500,
                cost: 3.92,
                current: 4.18,
                profit: 6.63,
                amount: 6270
            },
            {
                code: '510500',
                name: '中证500ETF',
                shares: 3000,
                cost: 6.78,
                current: 6.45,
                profit: -4.87,
                amount: 19350
            },
            {
                code: '159915',
                name: '创业板ETF',
                shares: 1800,
                cost: 2.34,
                current: 2.67,
                profit: 14.10,
                amount: 4806
            }
        ];

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadHoldingsData();
            bindEvents();
        });

        // 加载持仓数据
        function loadHoldingsData() {
            const holdingsList = document.getElementById('holdingsList');
            holdingsList.innerHTML = '';

            holdingsData.forEach(holding => {
                const profitClass = holding.profit >= 0 ? 'positive' : 'negative';
                const profitSign = holding.profit >= 0 ? '+' : '';

                const holdingItem = document.createElement('div');
                holdingItem.className = 'holding-item';
                holdingItem.innerHTML = `
                    <div class="holding-header">
                        <div>
                            <span class="holding-name">${holding.name}</span>
                            <span class="holding-code">${holding.code}</span>
                        </div>
                        <div class="holding-profit ${profitClass}">${profitSign}${holding.profit}%</div>
                    </div>
                    <div class="holding-details">
                        <span>持仓: ${holding.shares}</span>
                        <span>成本: ¥${holding.cost}</span>
                        <span>现价: ¥${holding.current}</span>
                        <span>市值: ¥${holding.amount.toLocaleString()}</span>
                    </div>
                `;

                holdingItem.addEventListener('click', () => {
                    showHoldingDetail(holding);
                });

                holdingsList.appendChild(holdingItem);
            });
        }

        // 绑定事件
        function bindEvents() {
            // 时间选择器
            const timeBtns = document.querySelectorAll('.time-btn');
            timeBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    timeBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    const days = this.dataset.days;
                    loadTrendData(days);
                });
            });
        }

        // 刷新数据
        function refreshData() {
            layui.use('layer', function(){
                const layer = layui.layer;
                const loadIndex = layer.load(1, {shade: [0.3, '#000']});

                setTimeout(() => {
                    layer.close(loadIndex);
                    layer.msg('数据已刷新', {icon: 1});
                    loadHoldingsData();
                    updateAssetData();
                }, 1500);
            });
        }

        // 更新资产数据
        function updateAssetData() {
            // 模拟数据更新
            const totalAsset = holdingsData.reduce((sum, item) => sum + item.amount, 0);
            const totalCost = holdingsData.reduce((sum, item) => sum + (item.shares * item.cost), 0);
            const profitRate = ((totalAsset - totalCost) / totalCost * 100).toFixed(2);

            document.getElementById('totalAsset').textContent = totalAsset.toLocaleString();
            document.getElementById('totalCost').textContent = '¥' + totalCost.toLocaleString();
            document.getElementById('profitRate').textContent = (profitRate >= 0 ? '+' : '') + profitRate + '%';
            document.getElementById('profitRate').className = 'detail-value ' + (profitRate >= 0 ? 'positive' : 'negative');
        }

        // 显示持仓详情
        function showHoldingDetail(holding) {
            layui.use('layer', function(){
                const layer = layui.layer;
                const content = `
                    <div style="padding: 20px;">
                        <h3>${holding.name} (${holding.code})</h3>
                        <div style="margin: 15px 0;">
                            <p>持仓份额: ${holding.shares}</p>
                            <p>成本价格: ¥${holding.cost}</p>
                            <p>当前价格: ¥${holding.current}</p>
                            <p>收益率: <span style="color: ${holding.profit >= 0 ? '#52c41a' : '#ff4d4f'}">${holding.profit >= 0 ? '+' : ''}${holding.profit}%</span></p>
                            <p>市值: ¥${holding.amount.toLocaleString()}</p>
                        </div>
                    </div>
                `;

                layer.open({
                    type: 1,
                    title: '持仓详情',
                    content: content,
                    area: ['400px', '300px'],
                    btn: ['编辑', '删除', '取消'],
                    yes: function(index) {
                        layer.close(index);
                        editHolding(holding);
                    },
                    btn2: function(index) {
                        layer.confirm('确定要删除这个持仓吗？', function(confirmIndex){
                            layer.close(confirmIndex);
                            layer.close(index);
                            deleteHolding(holding);
                        });
                        return false;
                    }
                });
            });
        }

        // 加载趋势数据
        function loadTrendData(days) {
            console.log('加载', days, '天的趋势数据');
        }

        // 导航功能
        function goToTrading() {
            alert('跳转到交易页面');
        }

        function goToTrend() {
            alert('当前就是趋势页面');
        }

        function goToSettings() {
            alert('跳转到设置页面');
        }

        function addHolding() {
            alert('跳转到添加持仓页面');
        }

        function editHolding(holding) {
            alert('编辑持仓: ' + holding.name);
        }

        function deleteHolding(holding) {
            layui.use('layer', function(){
                layui.layer.msg('删除成功', {icon: 1});
            });
        }

        // 初始化数据
        updateAssetData();
    </script>
</body>
</html>
