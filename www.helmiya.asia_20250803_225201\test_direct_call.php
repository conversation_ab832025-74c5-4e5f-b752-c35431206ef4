<?php
// 直接测试Investment控制器
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>直接测试Investment控制器</h2>";

// 设置基本环境
$_GET['m'] = 'App';
$_GET['c'] = 'Investment';
$_GET['a'] = 'test';

// 初始化ThinkPHP环境
if (!defined('APP_DEBUG')) {
    define("APP_DEBUG", true);
}
if (!defined('APP_PATH')) {
    define("APP_PATH", "./Application/");
}

echo "<h3>1. 检查文件是否存在</h3>";
$controllerFile = __DIR__ . '/Application/App/Controller/InvestmentController.class.php';
if (file_exists($controllerFile)) {
    echo "✅ InvestmentController.class.php 文件存在<br>";
    echo "文件大小: " . filesize($controllerFile) . " 字节<br>";
    echo "修改时间: " . date('Y-m-d H:i:s', filemtime($controllerFile)) . "<br>";
} else {
    echo "❌ InvestmentController.class.php 文件不存在<br>";
}

echo "<h3>2. 检查ThinkPHP框架</h3>";
$thinkPHPFile = __DIR__ . '/ThinkPHP/ThinkPHP.php';
if (file_exists($thinkPHPFile)) {
    echo "✅ ThinkPHP.php 文件存在<br>";
} else {
    echo "❌ ThinkPHP.php 文件不存在<br>";
}

echo "<h3>3. 尝试初始化ThinkPHP框架</h3>";
try {
    // 尝试初始化ThinkPHP
    if (file_exists('./ThinkPHP/ThinkPHP.php')) {
        echo "正在初始化ThinkPHP框架...<br>";
        require_once('./ThinkPHP/ThinkPHP.php');
        echo "✅ ThinkPHP框架初始化成功<br>";

        // 检查Think\Controller类是否存在
        if (class_exists('Think\\Controller')) {
            echo "✅ Think\\Controller 类存在<br>";
        } else {
            echo "❌ Think\\Controller 类不存在<br>";
        }

    } else {
        echo "❌ ThinkPHP.php 文件不存在<br>";
    }

} catch (Exception $e) {
    echo "❌ ThinkPHP初始化失败: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "❌ PHP错误: " . $e->getMessage() . "<br>";
}

echo "<h3>4. 测试URL访问</h3>";
$testUrls = [
    'https://www.helmiya.asia/index.php?m=App&c=Investment&a=test',
    'https://www.helmiya.asia/index.php/App/Investment/test',
    'https://www.helmiya.asia/App/Investment/test'
];

foreach ($testUrls as $url) {
    echo "<strong>测试URL:</strong> <a href='$url' target='_blank'>$url</a><br>";
}

echo "<h3>5. 检查其他控制器</h3>";
$zmControllerFile = __DIR__ . '/Application/App/Controller/ZmController.class.php';
if (file_exists($zmControllerFile)) {
    echo "✅ ZmController.class.php 文件存在 (对比用)<br>";
} else {
    echo "❌ ZmController.class.php 文件不存在<br>";
}

echo "<h3>6. 检查配置文件</h3>";
$configFile = __DIR__ . '/Application/Common/Conf/config.php';
if (file_exists($configFile)) {
    echo "✅ config.php 文件存在<br>";
    $config = include $configFile;
    echo "URL_MODEL: " . ($config['URL_MODEL'] ?? '未设置') . "<br>";
    echo "MODULE_ALLOW_LIST: " . implode(', ', $config['MODULE_ALLOW_LIST'] ?? []) . "<br>";
} else {
    echo "❌ config.php 文件不存在<br>";
}
?>
