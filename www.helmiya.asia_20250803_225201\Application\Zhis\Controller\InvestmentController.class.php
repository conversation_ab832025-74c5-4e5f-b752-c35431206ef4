<?php
namespace Zhis\Controller;
use Think\Controller;

/**
 * 投资记账控制器
 */
class InvestmentController extends Controller {

    /**
     * 投资记账页面
     */
    public function record() {
        $this->display();
    }

    /**
     * 检查数据库表结构
     */
    public function checkTables() {
        try {
            // 检查zm_etf_prices表
            $etfPricesStructure = M()->query("DESCRIBE zm_etf_prices");

            // 检查zm_holdings表
            $holdingsStructure = M()->query("DESCRIBE zm_holdings");

            // 检查zm_holdings_operations表
            $operationsStructure = M()->query("DESCRIBE zm_holdings_operations");

            // 获取一些示例数据
            $sampleEtfPrices = M()->query("SELECT * FROM zm_etf_prices LIMIT 3");
            $sampleHoldings = M()->query("SELECT * FROM zm_holdings LIMIT 3");
            $sampleOperations = M()->query("SELECT * FROM zm_holdings_operations LIMIT 3");

            echo json_encode([
                'code' => 1,
                'msg' => '检查成功',
                'data' => [
                    'etf_prices_structure' => $etfPricesStructure,
                    'holdings_structure' => $holdingsStructure,
                    'operations_structure' => $operationsStructure,
                    'sample_etf_prices' => $sampleEtfPrices,
                    'sample_holdings' => $sampleHoldings,
                    'sample_operations' => $sampleOperations
                ]
            ], JSON_UNESCAPED_UNICODE);

        } catch (Exception $e) {
            echo json_encode([
                'code' => 0,
                'msg' => '检查失败: ' . $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * 获取持仓数据API
     */
    public function getHoldingsData() {
        $user_id = I('get.user_id', 1, 'intval'); // 默认用户ID为1，实际应该从session获取
        
        try {
            // 获取持仓列表
            $holdings = M('holdings')
                ->where(['user_id' => $user_id, 'status' => 1])
                ->order('created_at DESC')
                ->select();

            $holdingsData = [];
            $totalCost = 0;
            $totalCurrentValue = 0;
            $totalShares = 0;

            foreach ($holdings as $holding) {
                // 获取最新价格 - 注意表名可能是zm_etf_price或zm_etf_prices
                $latestPrice = M()->query("SELECT * FROM zm_etf_prices WHERE symbol = '{$holding['symbol']}' ORDER BY trade_date DESC LIMIT 1");
                $latestPrice = $latestPrice ? $latestPrice[0] : null;
                
                $currentPrice = $latestPrice ? floatval($latestPrice['close_price']) : floatval($holding['avg_cost']);
                $currentValue = $currentPrice * floatval($holding['total_shares']);
                $costValue = floatval($holding['total_amount']);
                $profitLoss = $costValue > 0 ? (($currentValue - $costValue) / $costValue * 100) : 0;
                
                $holdingsData[] = [
                    'id' => $holding['id'],
                    'code' => $holding['symbol'],
                    'name' => $holding['name'],
                    'shares' => floatval($holding['total_shares']),
                    'cost' => floatval($holding['avg_cost']),
                    'current' => $currentPrice,
                    'profit' => round($profitLoss, 2),
                    'amount' => round($currentValue, 2),
                    'costAmount' => round($costValue, 2),
                    'changePercent' => $latestPrice ? floatval($latestPrice['change_percent']) : 0
                ];
                
                $totalCost += $costValue;
                $totalCurrentValue += $currentValue;
                $totalShares += floatval($holding['total_shares']);
            }
            
            // 计算总收益率
            $totalProfitRate = $totalCost > 0 ? (($totalCurrentValue - $totalCost) / $totalCost * 100) : 0;
            
            // 计算今日收益（模拟）
            $todayProfit = $totalCurrentValue * 0.012; // 假设今日涨幅1.2%
            
            $result = [
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'holdings' => $holdingsData,
                    'summary' => [
                        'totalAsset' => round($totalCurrentValue, 2),
                        'totalCost' => round($totalCost, 2),
                        'totalShares' => round($totalShares, 0),
                        'profitRate' => round($totalProfitRate, 2),
                        'todayProfit' => round($todayProfit, 2),
                        'holdingCount' => count($holdingsData)
                    ]
                ]
            ];
            
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            
        } catch (Exception $e) {
            echo json_encode([
                'code' => 0,
                'msg' => '获取数据失败: ' . $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * 获取趋势数据API
     */
    public function getTrendData() {
        $days = I('get.days', 30, 'intval');
        $user_id = I('get.user_id', 1, 'intval');
        
        try {
            // 获取用户持仓的ETF代码
            $holdings = M('holdings')
                ->field('symbol')
                ->where(['user_id' => $user_id, 'status' => 1])
                ->select();
            
            if (empty($holdings)) {
                echo json_encode([
                    'code' => 1,
                    'msg' => '暂无持仓数据',
                    'data' => []
                ], JSON_UNESCAPED_UNICODE);
                return;
            }
            
            $symbols = array_column($holdings, 'symbol');
            
            // 获取价格趋势数据
            $startDate = date('Y-m-d', strtotime("-{$days} days"));
            $symbolsStr = "'" . implode("','", $symbols) . "'";
            $priceData = M()->query("SELECT symbol, trade_date, close_price FROM zm_etf_prices WHERE symbol IN ({$symbolsStr}) AND trade_date >= '{$startDate}' ORDER BY trade_date ASC");
            
            // 按日期组织数据
            $trendData = [];
            foreach ($priceData as $price) {
                $date = $price['trade_date'];
                if (!isset($trendData[$date])) {
                    $trendData[$date] = [];
                }
                $trendData[$date][$price['symbol']] = floatval($price['close_price']);
            }
            
            // 计算每日总资产价值（简化计算）
            $assetTrend = [];
            foreach ($trendData as $date => $prices) {
                $totalValue = 0;
                foreach ($holdings as $holding) {
                    $symbol = $holding['symbol'];
                    if (isset($prices[$symbol])) {
                        // 这里需要获取该日期的持仓数量，简化处理使用当前持仓
                        $holdingInfo = M('holdings')
                            ->where(['user_id' => $user_id, 'symbol' => $symbol])
                            ->find();
                        if ($holdingInfo) {
                            $totalValue += $prices[$symbol] * floatval($holdingInfo['total_shares']);
                        }
                    }
                }
                
                if ($totalValue > 0) {
                    $assetTrend[] = [
                        'date' => $date,
                        'value' => round($totalValue, 2)
                    ];
                }
            }
            
            echo json_encode([
                'code' => 1,
                'msg' => '获取成功',
                'data' => $assetTrend
            ], JSON_UNESCAPED_UNICODE);
            
        } catch (Exception $e) {
            echo json_encode([
                'code' => 0,
                'msg' => '获取趋势数据失败: ' . $e->getMessage()
            ], JSON_UNESCAPED_UNICODE);
        }
    }
}
