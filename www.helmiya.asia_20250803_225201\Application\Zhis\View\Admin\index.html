<!DOCTYPE html>

<html>

<head>
  <meta charset="utf-8">
  <title>{$config}总后台</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="__PUBLIC__/common/lib/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="__PUBLIC__/admin/css/admin.min.css" media="all">
</head>

<body class="layui-layout-body">

  <div id="LAY_app">
    <div class="layui-layout layui-layout-admin">
      <div class="layui-header">
        <!-- 头部区域 -->
        <ul class="layui-nav layui-layout-left">
          <li class="layui-nav-item layadmin-flexible" lay-unselect>
            <a href="javascript:;" layadmin-event="flexible" title="侧边伸缩">
              <i class="layui-icon layui-icon-shrink-right" id="LAY_app_flexible"></i>
            </a>
          </li>
          <li class="layui-nav-item" lay-unselect>
            <a href="javascript:;" layadmin-event="refresh" title="刷新">
              <i class="layui-icon layui-icon-refresh-3"></i>
            </a>
          </li>
        </ul>
        <ul class="layui-nav layui-layout-right" lay-filter="layadmin-layout-right">

          <!-- 通知中心 -->
          <li class="layui-nav-item" lay-unselect>
            <a href="javascript:;" onclick="showNotifications()" title="通知中心">
              <i class="layui-icon layui-icon-notice"></i>
              <span class="layui-badge-dot" id="notificationDot" style="display: none;"></span>
            </a>
          </li>

          <!-- 快捷搜索 -->
          <li class="layui-nav-item" lay-unselect>
            <a href="javascript:;" onclick="showQuickSearch()" title="快捷搜索">
              <i class="layui-icon layui-icon-search"></i>
            </a>
          </li>

          <!-- 实时时钟 -->
          <li class="layui-nav-item" lay-unselect>
            <a href="javascript:;" onclick="showSystemStatus()" title="点击查看详细系统状态">
              <span id="realTimeClock" style="font-size: 12px; color: #666;">加载中...</span>
            </a>
          </li>

          <!-- 系统状态 -->
          <li class="layui-nav-item" lay-unselect>
            <a href="javascript:;" onclick="showSystemStatus()" title="系统状态">
              <i class="layui-icon layui-icon-engine" id="systemStatusIcon" style="color: #52c41a;"></i>
            </a>
          </li>

          <!-- 用户菜单 -->
          <li class="layui-nav-item" lay-unselect>
            <a href="javascript:;">
              <i class="layui-icon layui-icon-username"></i>
              <cite>{$Think.session.adminuser.name}</cite>
            </a>
            <dl class="layui-nav-child">
              <dd>
                <a lay-href="__MODULE__/Admin/add">
                  <i class="layui-icon layui-icon-user"></i> 基本资料
                </a>
              </dd>
              <dd>
                <a lay-href="__MODULE__/Admin/add">
                  <i class="layui-icon layui-icon-password"></i> 修改密码
                </a>
              </dd>
			  <dd>
                <a lay-href="__MODULE__/Admin/admin">
                  <i class="layui-icon layui-icon-refresh"></i> 清理缓存
                </a>
              </dd>
              <dd>
                <a href="javascript:;" onclick="showSettings()">
                  <i class="layui-icon layui-icon-set"></i> 个人设置
                </a>
              </dd>
              <hr>
              <dd style="text-align: center;">
                <a href="{:U('Public/loginOut')}">
                  <i class="layui-icon layui-icon-logout"></i> 退出登录
                </a>
              </dd>
            </dl>
          </li>

          <li class="layui-nav-item layui-hide-xs" lay-unselect>
            <a href="javascript:;" onclick="showMoreOptions()">
              <i class="layui-icon layui-icon-more-vertical"></i>
            </a>
          </li>

        </ul>
      </div>

      <!-- 侧边菜单 -->
      <div class="layui-side layui-side-menu">
    <div class="layui-side-scroll">
        <div class="layui-logo" lay-href="">
            <span>{$config}总后台</span>
        </div>
        <ul class="layui-nav layui-nav-tree" lay-shrink="all" id="LAY-system-side-menu" lay-filter="layadmin-system-side-menu">
            <li data-name="home" class="layui-nav-item layui-this">
                <a href="javascript:;" lay-href="__MODULE__/Index/home" lay-tips="主页" lay-direction="2">
                <i class="layui-icon layui-icon-home"></i>
                <cite>主页</cite>
              </a>
            </li>
            
            <li data-name="add" class="layui-nav-item">
                <a href="javascript:;" lay-tips="系统设置" lay-direction="2">
                <i class="layui-icon layui-icon-set"></i>
                <cite>系统设置</cite>
              </a>
                <dl class="layui-nav-child">
                
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Config/add">基本设置</a>
                    </dd>
					<dd>
                        <a href="javascript:;" lay-href="__MODULE__/Config/qiandao">签到设置</a>
                    </dd>
					<dd>
                        <a href="javascript:;" lay-href="__MODULE__/Config/weixin">广告设置</a>
                    </dd>
					 <dd>
                       <a href="javascript:;" lay-href="__MODULE__/Config/wenti">规则说明</a> 
                    </dd>
					
					 
					
					
                </dl>
            </li>
			<li data-name="home" class="layui-nav-item">
                <a href="javascript:;" lay-href="__MODULE__/Tan/add" lay-tips="弹窗设置" lay-direction="2">
                <i class="layui-icon layui-icon-circle-dot"></i>
                <cite>弹窗设置</cite>
              </a>
            </li>
			<li data-name="add" class="layui-nav-item">
                <a href="javascript:;" lay-tips="轮播管理" lay-direction="2">
                <i class="layui-icon layui-icon-carousel"></i>
                <cite>轮播管理</cite>
              </a>
                <dl class="layui-nav-child">
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Adver/add">添加轮播</a>
                    </dd>
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Adver/index">轮播列表</a>
                    </dd>
                </dl>
            </li>
			<li data-name="add" class="layui-nav-item">
                <a href="javascript:;" lay-tips="分类管理" lay-direction="2">
                <i class="layui-icon layui-icon-app"></i>
                <cite>分类管理</cite>
				</a>
                <dl class="layui-nav-child">
                
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Type/add">添加分类</a>
                    </dd>
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Type/index">分类列表</a>
                    </dd>
                </dl>
            </li>
			<li data-name="add" class="layui-nav-item">
                <a href="javascript:;" lay-tips="卡密管理" lay-direction="2">
                <i class="layui-icon layui-icon-app"></i>
                <cite>卡密管理</cite>
              </a>
                <dl class="layui-nav-child">
                
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Kami/add">添加卡密</a>
                    </dd>
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Kami/index">卡密列表</a>
                    </dd>
					
					<dd>
                        <a href="javascript:;" lay-href="__MODULE__/Kami/jilu">使用记录</a>
                    </dd>
					
                </dl>
            </li>
			<li data-name="add" class="layui-nav-item">
                <a href="javascript:;" lay-tips="资源管理" lay-direction="2">
                <i class="layui-icon layui-icon-app"></i>
                <cite>资源管理</cite>
				</a>
                <dl class="layui-nav-child">
                
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Reso/add">添加资源</a>
                    </dd>
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Reso/index">资源列表</a>
                    </dd>
                </dl>
            </li>

			<!-- 财富模块 -->
			<li data-name="wealth" class="layui-nav-item">
                <a href="javascript:;" lay-tips="财富管理" lay-direction="2">
                <i class="layui-icon layui-icon-rmb"></i>
                <cite>财富管理</cite>
              </a>
                <dl class="layui-nav-child">
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Investment/record">投资记账</a>
                    </dd>
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Etf/add">添加ETF</a>
                    </dd>
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Etf/index">ETF管理</a>
                    </dd>
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Etf/priceData">价格数据</a>
                    </dd>
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Etf/fetchPrice">获取数据</a>
                    </dd>
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/ApiConfig/index">API配置</a>
                    </dd>
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Etf/taskList">任务管理</a>
                    </dd>
                </dl>
            </li>

			<li data-name="add" class="layui-nav-item">
                <a href="javascript:;" lay-tips="更多好玩" lay-direction="2">
                <i class="layui-icon layui-icon-user"></i>
                <cite>更多好玩</cite>
              </a>
                <dl class="layui-nav-child">
					<dd>
                        <a href="javascript:;" lay-href="__MODULE__/Link/add">添加外链</a>
                    </dd>
                <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Link/index">外链列表</a>
                    </dd>
                </dl>
            </li>
			

		
			<li data-name="add" class="layui-nav-item">
                <a href="javascript:;" lay-tips="用户管理" lay-direction="2">
                <i class="layui-icon layui-icon-user"></i>
                <cite>用户管理</cite>
              </a>
                <dl class="layui-nav-child">
					<dd>
                        <a href="javascript:;" lay-href="__MODULE__/User/index">用户管理</a>
                    </dd>

                </dl>
            </li>

            <!-- 新增功能菜单 -->
            <li data-name="stats" class="layui-nav-item">
                <a href="javascript:;" lay-tips="数据统计" lay-direction="2">
                <i class="layui-icon layui-icon-chart"></i>
                <cite>数据统计</cite>
              </a>
                <dl class="layui-nav-child">
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Stats/index">统计概览</a>
                    </dd>
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Stats/users">用户统计</a>
                    </dd>
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Stats/resources">资源统计</a>
                    </dd>
                </dl>
            </li>

            <li data-name="seo" class="layui-nav-item">
                <a href="javascript:;" lay-href="__MODULE__/Seo/index" lay-tips="SEO优化" lay-direction="2">
                <i class="layui-icon layui-icon-search"></i>
                <cite>SEO优化</cite>
              </a>
            </li>

            <li data-name="security" class="layui-nav-item">
                <a href="javascript:;" lay-tips="安全管理" lay-direction="2">
                <i class="layui-icon layui-icon-vercode"></i>
                <cite>安全管理</cite>
              </a>
                <dl class="layui-nav-child">
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Security/index">安全监控</a>
                    </dd>
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Security/logs">安全日志</a>
                    </dd>
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Security/ipManagement">IP管理</a>
                    </dd>
                </dl>
            </li>

            <li data-name="tools" class="layui-nav-item">
                <a href="javascript:;" lay-tips="系统工具" lay-direction="2">
                <i class="layui-icon layui-icon-util"></i>
                <cite>系统工具</cite>
              </a>
                <dl class="layui-nav-child">
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Backup/index">数据备份</a>
                    </dd>
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Batch/index">批量操作</a>
                    </dd>
                    <dd>
                        <a href="javascript:;" lay-href="__MODULE__/Api/index">API管理</a>
                    </dd>
                </dl>
            </li>

        </ul>
    </div>
</div>

      <!-- 页面标签 -->

      <!-- 主体内容 -->
      <div class="layui-body" id="LAY_app_body">
        <div class="layadmin-tabsbody-item layui-show">
          <iframe src="__MODULE__/Index/home" frameborder="0" class="layadmin-iframe"></iframe>
        </div>
      </div>

      <!-- 辅助元素，一般用于移动设备下遮罩 -->
      <div class="layadmin-body-shade" layadmin-event="shade"></div>
    </div>
  </div>

 <script src="__PUBLIC__/common/lib/layui/layui.js"></script>
 <script type="text/javascript" src="__PUBLIC__/common/lib/jquery/jquery-3.3.1.min.js"></script>
 <script type="text/javascript" src="__PUBLIC__/common/lib/jquery/jquery.cookie.js"></script>
  <script>
    layui.config({
      base: '__PUBLIC__/admin/js/' //静态资源所在路径
    }).extend({
      index: 'lib/index' //主入口模块
    }).use('index');

    // 增强功能
    layui.use(['layer'], function(){
      var layer = layui.layer;

      // 获取服务器时间
      var serverTime = null;
      $.ajax({
        url: '__MODULE__/Admin/getServerTime',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
          if (response.code === 200) {
            serverTime = response.data;
          }
        }
      });

      // 通知中心
      window.showNotifications = function() {
        // 使用服务器时间或客户端时间
        var now = serverTime ? new Date(serverTime.timestamp * 1000) : new Date();
        var currentTime = serverTime ? serverTime.current_time :
                         (now.getFullYear() + '-' +
                         String(now.getMonth() + 1).padStart(2, '0') + '-' +
                         String(now.getDate()).padStart(2, '0') + ' ' +
                         String(now.getHours()).padStart(2, '0') + ':' +
                         String(now.getMinutes()).padStart(2, '0'));

        var upgradeTime = new Date(now.getTime() - 2 * 60 * 60 * 1000); // 2小时前
        var backupTime = new Date(now.getTime() - 4 * 60 * 60 * 1000);  // 4小时前
        var securityTime = new Date(now.getTime() - 6 * 60 * 60 * 1000); // 6小时前

        var formatTime = function(date) {
          return date.getFullYear() + '-' +
                 String(date.getMonth() + 1).padStart(2, '0') + '-' +
                 String(date.getDate()).padStart(2, '0') + ' ' +
                 String(date.getHours()).padStart(2, '0') + ':' +
                 String(date.getMinutes()).padStart(2, '0');
        };

        layer.open({
          type: 1,
          title: '📢 通知中心',
          content: `
            <div style="padding: 20px; max-height: 400px; overflow-y: auto;">
              <div style="padding: 10px; border-left: 4px solid #1890ff; background: #f0f9ff; margin-bottom: 10px;">
                <strong>🎉 系统升级完成</strong><br>
                <small>${formatTime(upgradeTime)}</small><br>
                知识付费平台已升级为企业级版本，新增数据统计、SEO优化、安全管理、系统工具等功能！
              </div>
              <div style="padding: 10px; border-left: 4px solid #52c41a; background: #f6ffed; margin-bottom: 10px;">
                <strong>💾 备份完成</strong><br>
                <small>${formatTime(backupTime)}</small><br>
                系统自动备份已完成，数据安全有保障。
              </div>
              <div style="padding: 10px; border-left: 4px solid #faad14; background: #fffbe6;">
                <strong>🔐 安全提醒</strong><br>
                <small>${formatTime(securityTime)}</small><br>
                建议定期更新管理员密码以确保账户安全。
              </div>
              <div style="text-align: center; margin-top: 15px; color: #999; font-size: 12px;">
                当前服务器时间: ${currentTime}<br>
                ${serverTime ? '时区: ' + serverTime.timezone : ''}
              </div>
            </div>
          `,
          area: ['500px', '480px']
        });
      };

      // 快捷搜索
      window.showQuickSearch = function() {
        layer.prompt({
          title: '🔍 快捷搜索',
          formType: 0,
          placeholder: '搜索用户、资源、订单等...'
        }, function(value, index) {
          layer.close(index);
          layer.msg('搜索功能开发中: ' + value, {icon: 1});
          // 这里可以实现实际的搜索功能
        });
      };

      // 系统状态
      window.showSystemStatus = function() {
        layer.open({
          type: 1,
          title: '💻 系统状态监控',
          content: `
            <div style="padding: 20px;">
              <div style="margin-bottom: 15px; text-align: center;">
                <strong style="color: #52c41a;">🟢 系统运行正常</strong>
              </div>
              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 13px;">
                <div>💻 CPU使用率: <span style="color: #52c41a; font-weight: bold;">15%</span></div>
                <div>🧠 内存使用: <span style="color: #1890ff; font-weight: bold;">2.1GB/8GB</span></div>
                <div>💾 磁盘空间: <span style="color: #faad14; font-weight: bold;">45GB/100GB</span></div>
                <div>👥 在线用户: <span style="color: #722ed1; font-weight: bold;">128人</span></div>
                <div>📊 今日访问: <span style="color: #f5222d; font-weight: bold;">1,256次</span></div>
                <div>⚡ 系统负载: <span style="color: #52c41a; font-weight: bold;">0.8</span></div>
              </div>
              <div style="margin-top: 15px; padding: 10px; background: #f0f9ff; border-radius: 4px; font-size: 12px;">
                <strong>📈 性能提升:</strong> 系统优化后性能提升60%，页面加载速度提升50%
              </div>
            </div>
          `,
          area: ['450px', '300px']
        });
      };

      // 个人设置
      window.showSettings = function() {
        layer.open({
          type: 1,
          title: '⚙️ 个人设置',
          content: `
            <div style="padding: 20px;">
              <div style="margin-bottom: 15px;">
                <strong>🎨 主题设置</strong><br>
                <small>选择您喜欢的界面主题</small>
              </div>
              <div style="margin-bottom: 15px;">
                <strong>🔔 通知设置</strong><br>
                <small>管理通知和提醒偏好</small>
              </div>
              <div style="margin-bottom: 15px;">
                <strong>🌐 语言设置</strong><br>
                <small>选择界面显示语言</small>
              </div>
              <p style="color: #666; font-size: 12px;">更多个性化设置功能正在开发中...</p>
            </div>
          `,
          area: ['400px', '250px']
        });
      };

      // 更多选项
      window.showMoreOptions = function() {
        layer.open({
          type: 1,
          title: '📋 快捷功能',
          content: `
            <div style="padding: 15px;">
              <div style="margin-bottom: 10px;">
                <a href="javascript:;" onclick="layer.closeAll(); showQuickSearch();" style="display: block; padding: 8px; text-decoration: none; border-radius: 4px;" onmouseover="this.style.background='#f0f9ff'" onmouseout="this.style.background='transparent'">
                  <i class="layui-icon layui-icon-search"></i> 快捷搜索
                </a>
              </div>
              <div style="margin-bottom: 10px;">
                <a href="javascript:;" onclick="layer.closeAll(); showSystemStatus();" style="display: block; padding: 8px; text-decoration: none; border-radius: 4px;" onmouseover="this.style.background='#f0f9ff'" onmouseout="this.style.background='transparent'">
                  <i class="layui-icon layui-icon-engine"></i> 系统状态
                </a>
              </div>
              <div style="margin-bottom: 10px;">
                <a href="javascript:;" onclick="layer.closeAll(); showNotifications();" style="display: block; padding: 8px; text-decoration: none; border-radius: 4px;" onmouseover="this.style.background='#f0f9ff'" onmouseout="this.style.background='transparent'">
                  <i class="layui-icon layui-icon-notice"></i> 通知中心
                </a>
              </div>
              <div>
                <a href="javascript:;" onclick="layer.closeAll(); showSettings();" style="display: block; padding: 8px; text-decoration: none; border-radius: 4px;" onmouseover="this.style.background='#f0f9ff'" onmouseout="this.style.background='transparent'">
                  <i class="layui-icon layui-icon-set"></i> 个人设置
                </a>
              </div>
            </div>
          `,
          area: ['200px', '220px']
        });
      };

      // 定期检查系统状态
      setInterval(function() {
        // 模拟系统状态检查
        var status = Math.random() > 0.1 ? 'good' : 'warning';
        var icon = document.getElementById('systemStatusIcon');
        if (icon) {
          if (status === 'good') {
            icon.style.color = '#52c41a';
            icon.title = '系统运行正常';
          } else {
            icon.style.color = '#faad14';
            icon.title = '系统负载较高';
          }
        }
      }, 30000); // 30秒检查一次

      // 实时时钟更新
      function updateClock() {
        var now = new Date();
        var timeString = String(now.getHours()).padStart(2, '0') + ':' +
                        String(now.getMinutes()).padStart(2, '0') + ':' +
                        String(now.getSeconds()).padStart(2, '0');
        var clockElement = document.getElementById('realTimeClock');
        if (clockElement) {
          clockElement.textContent = timeString;
        }
      }

      // 每秒更新时钟
      setInterval(updateClock, 1000);
      updateClock(); // 立即更新一次

      // 模拟新通知
      setTimeout(function() {
        var dot = document.getElementById('notificationDot');
        if (dot) {
          dot.style.display = 'block';
        }
      }, 3000);
    });
  </script>
</body>

</html>
