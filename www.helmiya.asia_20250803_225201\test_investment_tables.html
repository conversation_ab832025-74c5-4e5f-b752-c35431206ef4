<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>测试投资表结构</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .section h3 { margin-top: 0; color: #333; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 4px; overflow-x: auto; }
        button { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .loading { color: #666; }
        .error { color: #d32f2f; }
        .success { color: #388e3c; }
    </style>
</head>
<body>
    <h1>投资数据库表结构检查</h1>
    
    <div class="section">
        <h3>操作</h3>
        <button onclick="checkTables()">检查表结构</button>
        <button onclick="checkData()">检查数据</button>
    </div>
    
    <div class="section">
        <h3>检查结果</h3>
        <div id="result">点击上方按钮开始检查...</div>
    </div>

    <script>
        function checkTables() {
            document.getElementById('result').innerHTML = '<div class="loading">正在检查表结构...</div>';
            
            fetch('/Zhis/Investment/checkTables')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1) {
                        let html = '<div class="success">检查成功！</div>';
                        
                        // ETF价格表结构
                        html += '<h4>zm_etf_prices 表结构：</h4>';
                        html += '<pre>' + JSON.stringify(data.data.etf_prices_structure, null, 2) + '</pre>';
                        
                        // 持仓表结构
                        html += '<h4>zm_holdings 表结构：</h4>';
                        html += '<pre>' + JSON.stringify(data.data.holdings_structure, null, 2) + '</pre>';
                        
                        // 操作表结构
                        html += '<h4>zm_holdings_operations 表结构：</h4>';
                        html += '<pre>' + JSON.stringify(data.data.operations_structure, null, 2) + '</pre>';
                        
                        // 示例数据
                        if (data.data.sample_etf_prices && data.data.sample_etf_prices.length > 0) {
                            html += '<h4>ETF价格示例数据：</h4>';
                            html += '<pre>' + JSON.stringify(data.data.sample_etf_prices, null, 2) + '</pre>';
                        }
                        
                        if (data.data.sample_holdings && data.data.sample_holdings.length > 0) {
                            html += '<h4>持仓示例数据：</h4>';
                            html += '<pre>' + JSON.stringify(data.data.sample_holdings, null, 2) + '</pre>';
                        }
                        
                        if (data.data.sample_operations && data.data.sample_operations.length > 0) {
                            html += '<h4>操作示例数据：</h4>';
                            html += '<pre>' + JSON.stringify(data.data.sample_operations, null, 2) + '</pre>';
                        }
                        
                        document.getElementById('result').innerHTML = html;
                    } else {
                        document.getElementById('result').innerHTML = '<div class="error">检查失败: ' + data.msg + '</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('result').innerHTML = '<div class="error">网络错误: ' + error.message + '</div>';
                });
        }
        
        function checkData() {
            document.getElementById('result').innerHTML = '<div class="loading">正在检查数据...</div>';
            
            fetch('/Zhis/Investment/getHoldingsData?user_id=1')
                .then(response => response.json())
                .then(data => {
                    let html = '<h4>持仓数据API测试结果：</h4>';
                    html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                    document.getElementById('result').innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('result').innerHTML = '<div class="error">网络错误: ' + error.message + '</div>';
                });
        }
    </script>
</body>
</html>
