<?php
// 测试ThinkPHP是否正常工作
echo "<h2>ThinkPHP框架测试</h2>";

echo "<h3>测试链接</h3>";
echo "<p>请点击以下链接测试不同的访问方式：</p>";

echo "<h4>1. 测试ThinkPHP默认页面</h4>";
echo "<a href='https://www.helmiya.asia/index.php' target='_blank'>https://www.helmiya.asia/index.php</a><br>";

echo "<h4>2. 测试已知工作的控制器</h4>";
echo "<a href='https://www.helmiya.asia/App/zm/all' target='_blank'>https://www.helmiya.asia/App/zm/all</a> (已知工作)<br>";

echo "<h4>3. 测试Investment控制器 - 不同URL格式</h4>";
echo "<a href='https://www.helmiya.asia/index.php?m=App&c=Investment&a=test' target='_blank'>传统格式: ?m=App&c=Investment&a=test</a><br>";
echo "<a href='https://www.helmiya.asia/index.php/App/Investment/test' target='_blank'>REST格式: /App/Investment/test</a><br>";
echo "<a href='https://www.helmiya.asia/App/Investment/test' target='_blank'>短格式: /App/Investment/test</a><br>";

echo "<h4>4. 测试Holdings控制器</h4>";
echo "<a href='https://www.helmiya.asia/index.php?m=App&c=Holdings&a=getHoldingsList&user_id=2258' target='_blank'>传统格式: Holdings/getHoldingsList</a><br>";
echo "<a href='https://www.helmiya.asia/index.php/App/Holdings/getHoldingsList?user_id=2258' target='_blank'>REST格式: Holdings/getHoldingsList</a><br>";

echo "<h4>5. 测试其他App控制器</h4>";
echo "<a href='https://www.helmiya.asia/index.php?m=App&c=Zm&a=all' target='_blank'>传统格式: Zm/all</a><br>";

echo "<h3>说明</h3>";
echo "<p>如果看到JSON数据，说明接口工作正常</p>";
echo "<p>如果看到404错误，说明路由有问题</p>";
echo "<p>如果看到PHP错误，说明代码有问题</p>";
echo "<p>如果看到ThinkPHP错误页面，说明框架配置有问题</p>";
?>
