<!DOCTYPE html>
<html>
<head>
    <title>Investment API 测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 8px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Investment API 测试</h1>
    
    <div class="test-section">
        <h3>0. 简单控制器测试</h3>
        <button onclick="testSimple()">测试控制器是否可访问</button>
        <div id="testSimpleResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>1. 测试 testTable 接口</h3>
        <button onclick="testTableAPI()">测试表是否存在</button>
        <div id="testTableResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 测试 getInvestmentList 接口 (传统URL格式)</h3>
        <button onclick="testGetListTraditional()">测试获取投资列表 (传统格式)</button>
        <div id="getListTraditionalResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 测试 getInvestmentList 接口 (REST URL格式)</h3>
        <button onclick="testGetListREST()">测试获取投资列表 (REST格式)</button>
        <div id="getListRESTResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. 测试新的直接API</h3>
        <button onclick="testDirectAPI()">测试直接Investment API</button>
        <div id="directAPIResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>5. 测试其他已知工作的API</h3>
        <button onclick="testWorkingAPI()">测试 /App/zm/all</button>
        <div id="workingAPIResult" class="result"></div>
    </div>

    <script>
        function testSimple() {
            const resultDiv = document.getElementById('testSimpleResult');
            resultDiv.innerHTML = '测试中...';

            fetch('https://www.helmiya.asia/index.php?m=App&c=Investment&a=test')
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', response.headers);
                    return response.text();
                })
                .then(text => {
                    console.log('Response text:', text);
                    try {
                        const data = JSON.parse(text);
                        resultDiv.innerHTML = `<div class="success">成功: ${JSON.stringify(data, null, 2)}</div>`;
                    } catch (e) {
                        resultDiv.innerHTML = `<div class="error">返回的不是JSON: ${text.substring(0, 500)}</div>`;
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = `<div class="error">错误: ${error.message}</div>`;
                });
        }

        function testTableAPI() {
            const resultDiv = document.getElementById('testTableResult');
            resultDiv.innerHTML = '测试中...';
            
            fetch('https://www.helmiya.asia/index.php?m=App&c=Investment&a=testTable')
                .then(response => response.json())
                .then(data => {
                    resultDiv.innerHTML = `<div class="success">成功: ${JSON.stringify(data, null, 2)}</div>`;
                })
                .catch(error => {
                    resultDiv.innerHTML = `<div class="error">错误: ${error.message}</div>`;
                });
        }
        
        function testGetListTraditional() {
            const resultDiv = document.getElementById('getListTraditionalResult');
            resultDiv.innerHTML = '测试中...';

            fetch('https://www.helmiya.asia/index.php?m=App&c=Investment&a=getInvestmentList&user_id=2258')
                .then(response => {
                    console.log('getInvestmentList Response status:', response.status);
                    console.log('getInvestmentList Response headers:', response.headers);
                    return response.text();
                })
                .then(text => {
                    console.log('getInvestmentList Response text:', text);
                    try {
                        const data = JSON.parse(text);
                        resultDiv.innerHTML = `<div class="success">成功: ${JSON.stringify(data, null, 2)}</div>`;
                    } catch (e) {
                        resultDiv.innerHTML = `<div class="error">返回的不是JSON: ${text.substring(0, 1000)}</div>`;
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = `<div class="error">错误: ${error.message}</div>`;
                });
        }
        
        function testGetListREST() {
            const resultDiv = document.getElementById('getListRESTResult');
            resultDiv.innerHTML = '测试中...';
            
            fetch('https://www.helmiya.asia/index.php/App/Investment/getInvestmentList?user_id=2258')
                .then(response => response.json())
                .then(data => {
                    resultDiv.innerHTML = `<div class="success">成功: ${JSON.stringify(data, null, 2)}</div>`;
                })
                .catch(error => {
                    resultDiv.innerHTML = `<div class="error">错误: ${error.message}</div>`;
                });
        }
        
        function testDirectAPI() {
            const resultDiv = document.getElementById('directAPIResult');
            resultDiv.innerHTML = '测试中...';

            fetch('https://www.helmiya.asia/investment_api.php?action=getInvestmentList&user_id=2258')
                .then(response => {
                    console.log('Direct API Response status:', response.status);
                    return response.text();
                })
                .then(text => {
                    console.log('Direct API Response text:', text);
                    try {
                        const data = JSON.parse(text);
                        resultDiv.innerHTML = `<div class="success">成功: ${JSON.stringify(data, null, 2)}</div>`;
                    } catch (e) {
                        resultDiv.innerHTML = `<div class="error">返回的不是JSON: ${text.substring(0, 1000)}</div>`;
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = `<div class="error">错误: ${error.message}</div>`;
                });
        }

        function testWorkingAPI() {
            const resultDiv = document.getElementById('workingAPIResult');
            resultDiv.innerHTML = '测试中...';

            const formData = new FormData();
            formData.append('nextrow', '0');

            fetch('https://www.helmiya.asia/App/zm/all', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    resultDiv.innerHTML = `<div class="success">成功: ${JSON.stringify(data, null, 2)}</div>`;
                })
                .catch(error => {
                    resultDiv.innerHTML = `<div class="error">错误: ${error.message}</div>`;
                });
        }
    </script>
</body>
</html>
